'use client'

import { useState, useEffect } from 'react'
import { Item, testSupabaseConnection } from '@/lib/supabase'
import SearchInput from '@/components/SearchInput'
import NativeBarcodeScanner from '@/components/NativeBarcodeScanner'
import ItemActions from '@/components/ItemActions'
import ItemForm from '@/components/ItemForm'

type ViewMode = 'main' | 'actions' | 'create' | 'edit'

export default function Home() {
  const [viewMode, setViewMode] = useState<ViewMode>('main')
  const [selectedItem, setSelectedItem] = useState<Item | null>(null)
  const [newItemUpc, setNewItemUpc] = useState<string>('')
  const [scannerKey, setScannerKey] = useState(0) // Force scanner re-mount

  // Test Supabase connection on app load
  useEffect(() => {
    testSupabaseConnection()
  }, [])

  const handleItemSelect = (item: Item) => {
    setSelectedItem(item)
    setViewMode('actions')
  }

  const handleNewItem = (upc: string) => {
    setNewItemUpc(upc)
    setViewMode('create')
  }

  const handleScan = async (result: string) => {
    console.log('📱 Scanned:', result)

    // First check if item already exists
    try {
      const { itemsApi } = await import('@/lib/supabase')
      const existingItem = await itemsApi.getByUpc(result)

      if (existingItem) {
        // Item exists, go to actions
        handleItemSelect(existingItem)
      } else {
        // Item doesn't exist, create new
        handleNewItem(result)
      }
    } catch (error) {
      console.error('❌ Database error while checking for existing item:', error)
      alert('Database connection error. Please check your connection and try again.')
    }
  }

  const handleBackToMain = () => {
    setSelectedItem(null)
    setNewItemUpc('')
    setViewMode('main')
    // Force scanner to re-mount to prevent duplicate cameras
    setScannerKey(prev => prev + 1)
  }

  const handleItemSaved = (item: Item) => {
    setSelectedItem(item)
    setViewMode('actions')
  }

  const handleItemUpdate = (updatedItem: Item) => {
    setSelectedItem(updatedItem)
  }

  const handleEditItem = () => {
    setViewMode('edit')
  }

  const handleScannerError = (error: string) => {
    console.log('📱 Scanner error, search fallback available:', error)
  }

  const renderContent = () => {
    switch (viewMode) {
      case 'actions':
        return selectedItem ? (
          <ItemActions
            item={selectedItem}
            onBack={handleBackToMain}
            onEdit={handleEditItem}
            onItemUpdate={handleItemUpdate}
            onTransactionComplete={handleBackToMain}
          />
        ) : null

      case 'create':
        return (
          <ItemForm
            initialUpc={newItemUpc}
            onSave={handleItemSaved}
            onCancel={handleBackToMain}
          />
        )

      case 'edit':
        return selectedItem ? (
          <ItemForm
            item={selectedItem}
            onSave={handleItemSaved}
            onCancel={() => setViewMode('actions')}
          />
        ) : null

      default: // main - combined scanner and search
        return (
          <div className="space-y-6">
            {/* Barcode Scanner */}
            <div className="mb-6">
              <NativeBarcodeScanner
                key={scannerKey}
                onScan={handleScan}
                onError={handleScannerError}
              />
            </div>

            {/* Search Alternative */}
            <div className="border-t border-gray-700 pt-6">
              <div className="mb-4 text-center">
                <span className="text-gray-400 text-sm">
                  🔍 Or search for existing items
                </span>
              </div>
              <SearchInput
                onItemSelect={handleItemSelect}
                onNewItem={handleNewItem}
                placeholder="Search existing items by name or description..."
                autoFocus={false}
              />
            </div>


          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="max-w-4xl mx-auto p-4">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img src="/isc-logo.png" alt="ISC Logo" className="h-40 w-auto" />
          </div>
          <h1 className="text-3xl font-bold text-purple-400 mb-2">
            ISC Inventory Management System
          </h1>
          <p className="text-gray-400">
            Scan barcodes, search items, and manage your inventory
          </p>
        </div>

        {/* Main Content */}
        {renderContent()}

        {/* Footer */}
        <div className="mt-12 text-center text-gray-500 text-sm">
          <p>© 2024 Inventory Sales Company (ISC)</p>
          <p className="mt-1">Built with NextJS, TypeScript, and html5-qrcode</p>
        </div>
      </div>
    </div>
  )
}
